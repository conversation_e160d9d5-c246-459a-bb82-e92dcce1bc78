# @turf/planepoint

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## planepoint

Takes a triangular plane as a [Polygon][1]
and a [Point][2] within that triangle and returns the z-value
at that point. The Polygon should have properties `a`, `b`, and `c`
that define the values at its three corners. Alternatively, the z-values
of each triangle point can be provided by their respective 3rd coordinate
if their values are not provided as properties.

**Parameters**

-   `point` **[<PERSON>ord][3]** the Point for which a z-value will be calculated
-   `triangle` **[Feature][4]&lt;[Polygon][5]>** a Polygon feature with three vertices

**Examples**

```javascript
var point = turf.point([-75.3221, 39.529]);
// "a", "b", and "c" values represent the values of the coordinates in order.
var triangle = turf.polygon([[
  [-75.1221, 39.57],
  [-75.58, 39.18],
  [-75.97, 39.86],
  [-75.1221, 39.57]
]], {
  "a": 11,
  "b": 122,
  "c": 44
});

var zValue = turf.planepoint(point, triangle);
point.properties.zValue = zValue;

//addToMap
var addToMap = [triangle, point];
```

Returns **[number][6]** the z-value for `interpolatedPoint`

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/planepoint
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
