{"name": "j<PERSON><PERSON>", "version": "2.5.0", "author": "<PERSON> <<EMAIL>>", "description": "Create, read and edit .zip files with Javascript http://stuartk.com/jszip", "scripts": {"test": "npm run test-node && npm run test-browser", "test-node": "cd test && qunit -c node.js -t test.js", "test-browser": "grunt build && grunt test", "lint": "grunt jshint"}, "contributors": [{"name": "<PERSON>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>"}, {"name": "yim<PERSON><PERSON>"}], "main": "./lib/index", "repository": {"type": "git", "url": "https://github.com/Stuk/jszip.git"}, "keywords": ["zip", "deflate", "inflate"], "devDependencies": {"qunit": "~0.6.3", "grunt": "~0.4.1", "grunt-cli": "~0.1.9", "grunt-saucelabs": "~7.0.0", "grunt-contrib-connect": "~0.7.1", "jshint": "~2.5.1", "browserify": "~4.1.4", "grunt-browserify": "~2.1.0", "grunt-contrib-jshint": "~0.10.0", "grunt-contrib-uglify": "~0.4.0", "jszip-utils": "~0.0.2"}, "dependencies": {"pako": "~0.2.5"}, "license": "MIT or GPLv3"}