# @turf/center-mean

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## centerMean

Takes a [Feature][1] or [FeatureCollection][2] and returns the mean center. Can be weighted.

**Parameters**

-   `geojson` **[GeoJSON][3]** GeoJSON to be centered
-   `options` **[Object][4]** Optional parameters (optional, default `{}`)
    -   `options.properties` **[Object][4]** Translate GeoJSON Properties to Point (optional, default `{}`)
    -   `options.bbox` **[Object][4]** Translate GeoJSON BBox to Point (optional, default `{}`)
    -   `options.id` **[Object][4]** Translate GeoJSON Id to Point (optional, default `{}`)
    -   `options.weight` **[string][5]?** the property name used to weight the center

**Examples**

```javascript
var features = turf.featureCollection([
  turf.point([-97.522259, 35.4691], {value: 10}),
  turf.point([-97.502754, 35.463455], {value: 3}),
  turf.point([-97.508269, 35.463245], {value: 5})
]);

var options = {weight: "value"}
var mean = turf.centerMean(features, options);

//addToMap
var addToMap = [features, mean]
mean.properties['marker-size'] = 'large';
mean.properties['marker-color'] = '#000';
```

Returns **[Feature][6]&lt;[Point][7]>** a Point feature at the mean center point of all input features

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://tools.ietf.org/html/rfc7946#section-3

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[6]: https://tools.ietf.org/html/rfc7946#section-3.2

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/center-mean
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
