// https://d3js.org/d3-voronoi/ Version 1.1.2. Copyright 2017 <PERSON>.
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t(e.d3=e.d3||{})}(this,function(e){"use strict";function t(e){return e[0]}function n(e){return e[1]}function r(){this._=null}function i(e){e.U=e.C=e.L=e.R=e.P=e.N=null}function f(e,t){var n=t,r=t.R,i=n.U;i?i.L===n?i.L=r:i.R=r:e._=r,r.U=i,n.U=r,n.R=r.L,n.R&&(n.R.U=n),r.L=n}function u(e,t){var n=t,r=t.L,i=n.U;i?i.L===n?i.L=r:i.R=r:e._=r,r.U=i,n.U=r,n.L=r.R,n.L&&(n.L.U=n),r.R=n}function s(e){for(;e.L;)e=e.L;return e}function l(e,t,n,r){var i=[null,null],f=D.push(i)-1;return i.left=e,i.right=t,n&&o(i,e,t,n),r&&o(i,t,e,r),O[e.index].halfedges.push(f),O[t.index].halfedges.push(f),i}function a(e,t,n){var r=[t,n];return r.left=e,r}function o(e,t,n,r){e[0]||e[1]?e.left===n?e[1]=r:e[0]=r:(e[0]=r,e.left=t,e.right=n)}function h(e,t,n,r,i){var f,u=e[0],s=e[1],l=u[0],a=u[1],o=s[0],h=s[1],c=0,d=1,v=o-l,g=h-a;if(f=t-l,v||!(f>0)){if(f/=v,v<0){if(f<c)return;f<d&&(d=f)}else if(v>0){if(f>d)return;f>c&&(c=f)}if(f=r-l,v||!(f<0)){if(f/=v,v<0){if(f>d)return;f>c&&(c=f)}else if(v>0){if(f<c)return;f<d&&(d=f)}if(f=n-a,g||!(f>0)){if(f/=g,g<0){if(f<c)return;f<d&&(d=f)}else if(g>0){if(f>d)return;f>c&&(c=f)}if(f=i-a,g||!(f<0)){if(f/=g,g<0){if(f>d)return;f>c&&(c=f)}else if(g>0){if(f<c)return;f<d&&(d=f)}return!(c>0||d<1)||(c>0&&(e[0]=[l+c*v,a+c*g]),d<1&&(e[1]=[l+d*v,a+d*g]),!0)}}}}}function c(e,t,n,r,i){var f=e[1];if(f)return!0;var u,s,l=e[0],a=e.left,o=e.right,h=a[0],c=a[1],d=o[0],v=o[1],g=(h+d)/2,C=(c+v)/2;if(v===c){if(g<t||g>=r)return;if(h>d){if(l){if(l[1]>=i)return}else l=[g,n];f=[g,i]}else{if(l){if(l[1]<n)return}else l=[g,i];f=[g,n]}}else if(u=(h-d)/(v-c),s=C-u*g,u<-1||u>1)if(h>d){if(l){if(l[1]>=i)return}else l=[(n-s)/u,n];f=[(i-s)/u,i]}else{if(l){if(l[1]<n)return}else l=[(i-s)/u,i];f=[(n-s)/u,n]}else if(c<v){if(l){if(l[0]>=r)return}else l=[t,u*t+s];f=[r,u*r+s]}else{if(l){if(l[0]<t)return}else l=[r,u*r+s];f=[t,u*t+s]}return e[0]=l,e[1]=f,!0}function d(e,t,n,r){for(var i,f=D.length;f--;)c(i=D[f],e,t,n,r)&&h(i,e,t,n,r)&&(Math.abs(i[0][0]-i[1][0])>H||Math.abs(i[0][1]-i[1][1])>H)||delete D[f]}function v(e){return O[e.index]={site:e,halfedges:[]}}function g(e,t){var n=e.site,r=t.left,i=t.right;return n===i&&(i=r,r=n),i?Math.atan2(i[1]-r[1],i[0]-r[0]):(n===r?(r=t[1],i=t[0]):(r=t[0],i=t[1]),Math.atan2(r[0]-i[0],i[1]-r[1]))}function C(e,t){return t[+(t.left!==e.site)]}function p(e,t){return t[+(t.left===e.site)]}function L(){for(var e,t,n,r,i=0,f=O.length;i<f;++i)if((e=O[i])&&(r=(t=e.halfedges).length)){var u=new Array(r),s=new Array(r);for(n=0;n<r;++n)u[n]=n,s[n]=g(e,D[t[n]]);for(u.sort(function(e,t){return s[t]-s[e]}),n=0;n<r;++n)s[n]=t[u[n]];for(n=0;n<r;++n)t[n]=s[n]}}function R(e,t,n,r){var i,f,u,s,l,o,h,c,d,v,g,L,R=O.length,y=!0;for(i=0;i<R;++i)if(f=O[i]){for(u=f.site,l=f.halfedges,s=l.length;s--;)D[l[s]]||l.splice(s,1);for(s=0,o=l.length;s<o;)v=p(f,D[l[s]]),g=v[0],L=v[1],h=C(f,D[l[++s%o]]),c=h[0],d=h[1],(Math.abs(g-c)>H||Math.abs(L-d)>H)&&(l.splice(s,0,D.push(a(u,v,Math.abs(g-e)<H&&r-L>H?[e,Math.abs(c-e)<H?d:r]:Math.abs(L-r)<H&&n-g>H?[Math.abs(d-r)<H?c:n,r]:Math.abs(g-n)<H&&L-t>H?[n,Math.abs(c-n)<H?d:t]:Math.abs(L-t)<H&&g-e>H?[Math.abs(d-t)<H?c:e,t]:null))-1),++o);o&&(y=!1)}if(y){var b,M,U,x=1/0;for(i=0,y=null;i<R;++i)(f=O[i])&&(u=f.site,b=u[0]-e,M=u[1]-t,(U=b*b+M*M)<x&&(x=U,y=f));if(y){var N=[e,t],P=[e,r],_=[n,r],k=[n,t];y.halfedges.push(D.push(a(u=y.site,N,P))-1,D.push(a(u,P,_))-1,D.push(a(u,_,k))-1,D.push(a(u,k,N))-1)}}for(i=0;i<R;++i)(f=O[i])&&(f.halfedges.length||delete O[i])}function y(){i(this),this.x=this.y=this.arc=this.site=this.cy=null}function b(e){var t=e.P,n=e.N;if(t&&n){var r=t.site,i=e.site,f=n.site;if(r!==f){var u=i[0],s=i[1],l=r[0]-u,a=r[1]-s,o=f[0]-u,h=f[1]-s,c=2*(l*h-a*o);if(!(c>=-I)){var d=l*l+a*a,v=o*o+h*h,g=(h*d-a*v)/c,C=(l*v-o*d)/c,p=F.pop()||new y;p.arc=e,p.site=i,p.x=g+u,p.y=(p.cy=C+s)+Math.sqrt(g*g+C*C),e.circle=p;for(var L=null,R=B._;R;)if(p.y<R.y||p.y===R.y&&p.x<=R.x){if(!R.L){L=R.P;break}R=R.L}else{if(!R.R){L=R;break}R=R.R}B.insert(L,p),L||(E=p)}}}}function M(e){var t=e.circle;t&&(t.P||(E=t.N),B.remove(t),F.push(t),i(t),e.circle=null)}function U(){i(this),this.edge=this.site=this.circle=null}function x(e){var t=G.pop()||new U;return t.site=e,t}function N(e){M(e),z.remove(e),G.push(e),i(e)}function P(e){var t=e.circle,n=t.x,r=t.cy,i=[n,r],f=e.P,u=e.N,s=[e];N(e);for(var a=f;a.circle&&Math.abs(n-a.circle.x)<H&&Math.abs(r-a.circle.cy)<H;)f=a.P,s.unshift(a),N(a),a=f;s.unshift(a),M(a);for(var h=u;h.circle&&Math.abs(n-h.circle.x)<H&&Math.abs(r-h.circle.cy)<H;)u=h.N,s.push(h),N(h),h=u;s.push(h),M(h);var c,d=s.length;for(c=1;c<d;++c)h=s[c],a=s[c-1],o(h.edge,a.site,h.site,i);a=s[0],h=s[d-1],h.edge=l(a.site,h.site,null,i),b(a),b(h)}function _(e){for(var t,n,r,i,f=e[0],u=e[1],s=z._;s;)if((r=k(s,u)-f)>H)s=s.L;else{if(!((i=f-w(s,u))>H)){r>-H?(t=s.P,n=s):i>-H?(t=s,n=s.N):t=n=s;break}if(!s.R){t=s;break}s=s.R}v(e);var a=x(e);if(z.insert(t,a),t||n){if(t===n)return M(t),n=x(t.site),z.insert(a,n),a.edge=n.edge=l(t.site,a.site),b(t),void b(n);if(!n)return void(a.edge=l(t.site,a.site));M(t),M(n);var h=t.site,c=h[0],d=h[1],g=e[0]-c,C=e[1]-d,p=n.site,L=p[0]-c,R=p[1]-d,y=2*(g*R-C*L),U=g*g+C*C,N=L*L+R*R,P=[(R*U-C*N)/y+c,(g*N-L*U)/y+d];o(n.edge,h,p,P),a.edge=l(h,e,null,P),n.edge=l(e,p,null,P),b(t),b(n)}}function k(e,t){var n=e.site,r=n[0],i=n[1],f=i-t;if(!f)return r;var u=e.P;if(!u)return-(1/0);n=u.site;var s=n[0],l=n[1],a=l-t;if(!a)return s;var o=s-r,h=1/f-1/a,c=o/a;return h?(-c+Math.sqrt(c*c-2*h*(o*o/(-2*a)-l+a/2+i-f/2)))/h+r:(r+s)/2}function w(e,t){var n=e.N;if(n)return k(n,t);var r=e.site;return r[1]===t?r[0]:1/0}function m(e,t,n){return(e[0]-n[0])*(t[1]-e[1])-(e[0]-t[0])*(n[1]-e[1])}function A(e,t){return t[1]-e[1]||t[0]-e[0]}function j(e,t){var n,i,f,u=e.sort(A).pop();for(D=[],O=new Array(e.length),z=new r,B=new r;;)if(f=E,u&&(!f||u[1]<f.y||u[1]===f.y&&u[0]<f.x))u[0]===n&&u[1]===i||(_(u),n=u[0],i=u[1]),u=e.pop();else{if(!f)break;P(f.arc)}if(L(),t){var s=+t[0][0],l=+t[0][1],a=+t[1][0],o=+t[1][1];d(s,l,a,o),R(s,l,a,o)}this.edges=D,this.cells=O,z=B=D=O=null}var q=function(e){return function(){return e}};r.prototype={constructor:r,insert:function(e,t){var n,r,i;if(e){if(t.P=e,t.N=e.N,e.N&&(e.N.P=t),e.N=t,e.R){for(e=e.R;e.L;)e=e.L;e.L=t}else e.R=t;n=e}else this._?(e=s(this._),t.P=null,t.N=e,e.P=e.L=t,n=e):(t.P=t.N=null,this._=t,n=null);for(t.L=t.R=null,t.U=n,t.C=!0,e=t;n&&n.C;)r=n.U,n===r.L?(i=r.R,i&&i.C?(n.C=i.C=!1,r.C=!0,e=r):(e===n.R&&(f(this,n),e=n,n=e.U),n.C=!1,r.C=!0,u(this,r))):(i=r.L,i&&i.C?(n.C=i.C=!1,r.C=!0,e=r):(e===n.L&&(u(this,n),e=n,n=e.U),n.C=!1,r.C=!0,f(this,r))),n=e.U;this._.C=!1},remove:function(e){e.N&&(e.N.P=e.P),e.P&&(e.P.N=e.N),e.N=e.P=null;var t,n,r,i=e.U,l=e.L,a=e.R;if(n=l?a?s(a):l:a,i?i.L===e?i.L=n:i.R=n:this._=n,l&&a?(r=n.C,n.C=e.C,n.L=l,l.U=n,n!==a?(i=n.U,n.U=e.U,e=n.R,i.L=e,n.R=a,a.U=n):(n.U=i,i=n,e=n.R)):(r=e.C,e=n),e&&(e.U=i),!r){if(e&&e.C)return void(e.C=!1);do{if(e===this._)break;if(e===i.L){if(t=i.R,t.C&&(t.C=!1,i.C=!0,f(this,i),t=i.R),t.L&&t.L.C||t.R&&t.R.C){t.R&&t.R.C||(t.L.C=!1,t.C=!0,u(this,t),t=i.R),t.C=i.C,i.C=t.R.C=!1,f(this,i),e=this._;break}}else if(t=i.L,t.C&&(t.C=!1,i.C=!0,u(this,i),t=i.L),t.L&&t.L.C||t.R&&t.R.C){t.L&&t.L.C||(t.R.C=!1,t.C=!0,f(this,t),t=i.L),t.C=i.C,i.C=t.L.C=!1,u(this,i),e=this._;break}t.C=!0,e=i,i=i.U}while(!e.C);e&&(e.C=!1)}}};var E,z,O,B,D,F=[],G=[],H=1e-6,I=1e-12;j.prototype={constructor:j,polygons:function(){var e=this.edges;return this.cells.map(function(t){var n=t.halfedges.map(function(n){return C(t,e[n])});return n.data=t.site.data,n})},triangles:function(){var e=[],t=this.edges;return this.cells.forEach(function(n,r){if(f=(i=n.halfedges).length)for(var i,f,u,s=n.site,l=-1,a=t[i[f-1]],o=a.left===s?a.right:a.left;++l<f;)u=o,a=t[i[l]],o=a.left===s?a.right:a.left,u&&o&&r<u.index&&r<o.index&&m(s,u,o)<0&&e.push([s.data,u.data,o.data])}),e},links:function(){return this.edges.filter(function(e){return e.right}).map(function(e){return{source:e.left.data,target:e.right.data}})},find:function(e,t,n){for(var r,i,f=this,u=f._found||0,s=f.cells.length;!(i=f.cells[u]);)if(++u>=s)return null;var l=e-i.site[0],a=t-i.site[1],o=l*l+a*a;do{i=f.cells[r=u],u=null,i.halfedges.forEach(function(n){var r=f.edges[n],s=r.left;if(s!==i.site&&s||(s=r.right)){var l=e-s[0],a=t-s[1],h=l*l+a*a;h<o&&(o=h,u=s.index)}})}while(null!==u);return f._found=r,null==n||o<=n*n?i.site:null}};var J=function(){function e(e){return new j(e.map(function(t,n){var f=[Math.round(r(t,n,e)/H)*H,Math.round(i(t,n,e)/H)*H];return f.index=n,f.data=t,f}),f)}var r=t,i=n,f=null;return e.polygons=function(t){return e(t).polygons()},e.links=function(t){return e(t).links()},e.triangles=function(t){return e(t).triangles()},e.x=function(t){return arguments.length?(r="function"==typeof t?t:q(+t),e):r},e.y=function(t){return arguments.length?(i="function"==typeof t?t:q(+t),e):i},e.extent=function(t){return arguments.length?(f=null==t?null:[[+t[0][0],+t[0][1]],[+t[1][0],+t[1][1]]],e):f&&[[f[0][0],f[0][1]],[f[1][0],f[1][1]]]},e.size=function(t){return arguments.length?(f=null==t?null:[[0,0],[+t[0],+t[1]]],e):f&&[f[1][0]-f[0][0],f[1][1]-f[0][1]]},e};e.voronoi=J,Object.defineProperty(e,"__esModule",{value:!0})});