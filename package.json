{"name": "shapefile-conversion-api", "version": "1.0.0", "description": "API for converting single-part shapefiles to multi-part polygons", "main": "shapefile-api.js", "scripts": {"start": "node shapefile-api.js", "dev": "nodemon shapefile-api.js", "test": "jest"}, "keywords": ["shapefile", "GIS", "spatial", "polygon", "multipart"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "fs-extra": "^11.1.1", "unzipper": "^0.10.14", "archiver": "^6.0.1", "shapefile": "^0.6.6", "shp-write": "^0.3.2", "@turf/turf": "^6.5.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.5.0", "supertest": "^6.3.3"}, "engines": {"node": ">=14.0.0"}}