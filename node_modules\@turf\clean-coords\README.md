# @turf/clean-coords

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## cleanCoords

Removes redundant coordinates from any GeoJSON Geometry.

**Parameters**

-   `geojson` **([Geometry][1] \| [Feature][2])** Feature or Geometry
-   `options` **[Object][3]** Optional parameters (optional, default `{}`)
    -   `options.mutate` **[boolean][4]** allows GeoJSON input to be mutated (optional, default `false`)

**Examples**

```javascript
var line = turf.lineString([[0, 0], [0, 2], [0, 5], [0, 8], [0, 8], [0, 10]]);
var multiPoint = turf.multiPoint([[0, 0], [0, 0], [2, 2]]);

turf.cleanCoords(line).geometry.coordinates;
//= [[0, 0], [0, 10]]

turf.cleanCoords(multiPoint).geometry.coordinates;
//= [[0, 0], [2, 2]]
```

Returns **([Geometry][1] \| [Feature][2])** the cleaned input Feature/Geometry

[1]: https://tools.ietf.org/html/rfc7946#section-3.1

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/clean-coords
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
