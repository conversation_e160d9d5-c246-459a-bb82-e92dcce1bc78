# @turf/hex-grid

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## hexGrid

Takes a bounding box and the diameter of the cell and returns a [FeatureCollection][1] of flat-topped
hexagons or triangles ([Polygon][2] features) aligned in an "odd-q" vertical grid as
described in [Hexagonal Grids][3].

**Parameters**

-   `bbox` **[BBox][4]** extent in [minX, minY, maxX, maxY] order
-   `cellSide` **[number][5]** length of the side of the the hexagons or triangles, in units. It will also coincide with the
    radius of the circumcircle of the hexagons.
-   `options` **[Object][6]** Optional parameters (optional, default `{}`)
    -   `options.units` **[string][7]** used in calculating cell size, can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)
    -   `options.properties` **[Object][6]** passed to each hexagon or triangle of the grid (optional, default `{}`)
    -   `options.mask` **[Feature][8]&lt;[Polygon][9]>?** if passed a Polygon or MultiPolygon, the grid Points will be created only inside it
    -   `options.triangles` **[boolean][10]** whether to return as triangles instead of hexagons (optional, default `false`)

**Examples**

```javascript
var bbox = [-96,31,-84,40];
var cellSide = 50;
var options = {units: 'miles'};

var hexgrid = turf.hexGrid(bbox, cellSide, options);

//addToMap
var addToMap = [hexgrid];
```

Returns **[FeatureCollection][11]&lt;[Polygon][9]>** a hexagonal grid

[1]: https://tools.ietf.org/html/rfc7946#section-3.3

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[3]: http://www.redblobgames.com/grids/hexagons/

[4]: https://tools.ietf.org/html/rfc7946#section-5

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[8]: https://tools.ietf.org/html/rfc7946#section-3.2

[9]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[10]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[11]: https://tools.ietf.org/html/rfc7946#section-3.3

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/hex-grid
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
