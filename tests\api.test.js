const request = require('supertest');
const fs = require('fs-extra');
const path = require('path');
const app = require('../shapefile-api');

describe('Shapefile Conversion API', () => {
  let server;
  
  beforeAll(() => {
    // Start the server for testing
    server = app.listen(0); // Use port 0 for random available port
  });

  afterAll(async () => {
    // Close the server after tests
    if (server) {
      await new Promise((resolve) => server.close(resolve));
    }
  });

  beforeEach(async () => {
    // Clean up test directories before each test
    await fs.remove('uploads').catch(() => {});
    await fs.remove('output').catch(() => {});
    await fs.ensureDir('uploads');
    await fs.ensureDir('output');
  });

  afterEach(async () => {
    // Clean up test directories after each test
    await fs.remove('uploads').catch(() => {});
    await fs.remove('output').catch(() => {});
  });

  describe('GET /api/health', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body).toEqual({
        status: 'healthy',
        service: 'Shapefile Conversion API',
        version: '1.0.0'
      });
    });
  });

  describe('POST /api/analyze', () => {
    it('should return 400 when no file is uploaded', async () => {
      const response = await request(app)
        .post('/api/analyze')
        .expect(400);

      expect(response.body).toEqual({
        error: 'No file uploaded'
      });
    });

    it('should analyze a valid shapefile', async () => {
      // Create a mock shapefile zip for testing
      const mockZipPath = await createMockShapefileZip();
      
      const response = await request(app)
        .post('/api/analyze')
        .attach('shapefile', mockZipPath)
        .expect(200);

      expect(response.body).toHaveProperty('featureCount');
      expect(response.body).toHaveProperty('features');
      expect(response.body).toHaveProperty('ready');
      expect(Array.isArray(response.body.features)).toBe(true);
    });
  });

  describe('POST /api/convert', () => {
    it('should return 400 when no file is uploaded', async () => {
      const response = await request(app)
        .post('/api/convert')
        .expect(400);

      expect(response.body).toEqual({
        error: 'No file uploaded'
      });
    });

    it('should handle invalid zip files', async () => {
      // Create an invalid zip file
      const invalidZipPath = path.join(__dirname, 'invalid.zip');
      await fs.writeFile(invalidZipPath, 'not a zip file');

      const response = await request(app)
        .post('/api/convert')
        .attach('shapefile', invalidZipPath)
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Conversion failed');

      // Cleanup
      await fs.remove(invalidZipPath);
    });
  });

  describe('Error Handling', () => {
    it('should handle missing shapefile in zip', async () => {
      // Create a zip without shapefile
      const emptyZipPath = await createEmptyZip();
      
      const response = await request(app)
        .post('/api/convert')
        .attach('shapefile', emptyZipPath)
        .expect(500);

      expect(response.body).toHaveProperty('error');
      expect(response.body.error).toBe('Conversion failed');
    });
  });

  describe('File Upload Limits', () => {
    it('should handle large file uploads within limits', async () => {
      // This test would require a large file, skipping for now
      // In a real scenario, you'd test with files approaching the 50MB limit
    });
  });
});

// Helper functions for creating test files
async function createMockShapefileZip() {
  const archiver = require('archiver');
  const mockZipPath = path.join(__dirname, 'mock_shapefile.zip');
  
  // Create a simple mock shapefile structure
  const output = fs.createWriteStream(mockZipPath);
  const archive = archiver('zip');
  
  archive.pipe(output);
  
  // Add mock files (these would be actual shapefile components in real testing)
  archive.append('mock shapefile content', { name: 'test.shp' });
  archive.append('mock index content', { name: 'test.shx' });
  archive.append('mock database content', { name: 'test.dbf' });
  
  await archive.finalize();
  
  return new Promise((resolve, reject) => {
    output.on('close', () => resolve(mockZipPath));
    output.on('error', reject);
  });
}

async function createEmptyZip() {
  const archiver = require('archiver');
  const emptyZipPath = path.join(__dirname, 'empty.zip');
  
  const output = fs.createWriteStream(emptyZipPath);
  const archive = archiver('zip');
  
  archive.pipe(output);
  archive.append('some text file', { name: 'readme.txt' });
  
  await archive.finalize();
  
  return new Promise((resolve, reject) => {
    output.on('close', () => resolve(emptyZipPath));
    output.on('error', reject);
  });
}
