{"name": "d3-voronoi", "version": "1.1.2", "description": "Compute the Voronoi diagram of a set of two-dimensional points.", "keywords": ["d3", "d3-module", "voronoi", "delaunay"], "homepage": "https://d3js.org/d3-voronoi/", "license": "BSD-3-<PERSON><PERSON>", "author": {"name": "<PERSON>", "url": "http://bost.ocks.org/mike"}, "main": "build/d3-voronoi.js", "module": "index", "jsnext:main": "index", "repository": {"type": "git", "url": "https://github.com/d3/d3-voronoi.git"}, "scripts": {"pretest": "rm -rf build && mkdir build && rollup --banner \"$(preamble)\" -f umd -n d3 -o build/d3-voronoi.js -- index.js", "test": "tape 'test/**/*-test.js' && eslint index.js src", "prepublish": "npm run test && uglifyjs --preamble \"$(preamble)\" build/d3-voronoi.js -c -m -o build/d3-voronoi.min.js", "postpublish": "git push && git push --tags && cd ../d3.github.com && git pull && cp ../d3-voronoi/build/d3-voronoi.js d3-voronoi.v1.js && cp ../d3-voronoi/build/d3-voronoi.min.js d3-voronoi.v1.min.js && git add d3-voronoi.v1.js d3-voronoi.v1.min.js && git commit -m \"d3-voronoi ${npm_package_version}\" && git push && cd - && zip -j build/d3-voronoi.zip -- LICENSE README.md build/d3-voronoi.js build/d3-voronoi.min.js"}, "devDependencies": {"eslint": "3", "package-preamble": "0.0", "rollup": "0.41", "tape": "4", "uglify-js": "^2.8.11"}}