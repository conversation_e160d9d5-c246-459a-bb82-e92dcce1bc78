# @turf/point-grid

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## pointGrid

Creates a [Point][1] grid from a bounding box, [FeatureCollection][2] or [Feature][3].

**Parameters**

-   `bbox` **[Array][4]&lt;[number][5]>** extent in [minX, minY, maxX, maxY] order
-   `cellSide` **[number][5]** the distance between points, in units
-   `options` **[Object][6]** Optional parameters (optional, default `{}`)
    -   `options.units` **[string][7]** used in calculating cellSide, can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)
    -   `options.mask` **[Feature][8]&lt;([Polygon][9] \| [MultiPolygon][10])>?** if passed a Polygon or MultiPolygon, the grid Points will be created only inside it
    -   `options.properties` **[Object][6]** passed to each point of the grid (optional, default `{}`)

**Examples**

```javascript
var extent = [-70.823364, -33.553984, -70.473175, -33.302986];
var cellSide = 3;
var options = {units: 'miles'};

var grid = turf.pointGrid(extent, cellSide, options);

//addToMap
var addToMap = [grid];
```

Returns **[FeatureCollection][11]&lt;[Point][12]>** grid of points

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[8]: https://tools.ietf.org/html/rfc7946#section-3.2

[9]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[10]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[11]: https://tools.ietf.org/html/rfc7946#section-3.3

[12]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/point-grid
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
