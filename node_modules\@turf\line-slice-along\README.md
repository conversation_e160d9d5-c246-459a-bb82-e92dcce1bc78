# @turf/line-slice-along

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineSliceAlong

Takes a [line][1], a specified distance along the line to a start [Point][2],
and a specified  distance along the line to a stop point
and returns a subsection of the line in-between those points.

This can be useful for extracting only the part of a route between two distances.

**Parameters**

-   `line` **([Feature][3]&lt;[LineString][4]> | [LineString][4])** input line
-   `startDist` **[number][5]** distance along the line to starting point
-   `stopDist` **[number][5]** distance along the line to ending point
-   `options` **[Object][6]** Optional parameters (optional, default `{}`)
    -   `options.units` **[string][7]** can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)

**Examples**

```javascript
var line = turf.lineString([[7, 45], [9, 45], [14, 40], [14, 41]]);
var start = 12.5;
var stop = 25;
var sliced = turf.lineSliceAlong(line, start, stop, {units: 'miles'});

//addToMap
var addToMap = [line, start, stop, sliced]
```

Returns **[Feature][3]&lt;[LineString][4]>** sliced line

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/line-slice-along
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
