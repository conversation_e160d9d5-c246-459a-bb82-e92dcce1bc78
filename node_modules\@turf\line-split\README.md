# @turf/line-split

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineSplit

Split a LineString by another GeoJSON Feature.

**Parameters**

-   `line` **[Feature][1]&lt;[LineString][2]>** LineString Feature to split
-   `splitter` **[Feature][1]&lt;any>** Feature used to split line

**Examples**

```javascript
var line = turf.lineString([[120, -25], [145, -25]]);
var splitter = turf.lineString([[130, -15], [130, -35]]);

var split = turf.lineSplit(line, splitter);

//addToMap
var addToMap = [line, splitter]
```

Returns **[FeatureCollection][3]&lt;[LineString][2]>** Split LineStrings

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[3]: https://tools.ietf.org/html/rfc7946#section-3.3

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/line-split
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
