# @turf/point-on-feature

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## pointOnFeature

Takes a Feature or FeatureCollection and returns a [Point][1] guaranteed to be on the surface of the feature.

-   Given a [Polygon][2], the point will be in the area of the polygon
-   Given a [LineString][3], the point will be along the string
-   Given a [Point][1], the point will the same as the input

**Parameters**

-   `geojson` **[GeoJSON][4]** any Feature or FeatureCollection

**Examples**

```javascript
var polygon = turf.polygon([[
  [116, -36],
  [131, -32],
  [146, -43],
  [155, -25],
  [133, -9],
  [111, -22],
  [116, -36]
]]);

var pointOnPolygon = turf.pointOnFeature(polygon);

//addToMap
var addToMap = [polygon, pointOnPolygon];
```

Returns **[Feature][5]&lt;[Point][6]>** a point on the surface of `input`

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://tools.ietf.org/html/rfc7946#section-3

[5]: https://tools.ietf.org/html/rfc7946#section-3.2

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/point-on-feature
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
