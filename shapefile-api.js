// app.js - Main API Server
const express = require('express');
const multer = require('multer');
const fs = require('fs-extra');
const path = require('path');
const unzipper = require('unzipper');
const archiver = require('archiver');
const shapefile = require('shapefile');
const shpwrite = require('shp-write');
const turf = require('@turf/turf');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// Enable CORS for cross-origin requests
app.use(cors());
app.use(express.json());

// Configure multer for file uploads
const upload = multer({
  dest: 'uploads/',
  limits: { fileSize: 50 * 1024 * 1024 } // 50MB limit
});

// Ensure directories exist
fs.ensureDirSync('uploads');
fs.ensureDirSync('output');

/**
 * Recursively find shapefile in directory and subdirectories
 */
async function findShapefileRecursively(dir) {
  const files = await fs.readdir(dir);

  // First, look for .shp files in current directory
  for (const file of files) {
    if (file.endsWith('.shp')) {
      return {
        fileName: file,
        fullPath: path.join(dir, file)
      };
    }
  }

  // If not found, search in subdirectories
  for (const file of files) {
    const fullPath = path.join(dir, file);
    const stat = await fs.stat(fullPath);

    if (stat.isDirectory()) {
      const result = await findShapefileRecursively(fullPath);
      if (result) {
        return result;
      }
    }
  }

  return null;
}

/**
 * Convert single-part polygons to multi-part polygon with area calculations
 */
async function processShapefile(inputDir, outputDir) {
  try {
    // Find the shapefile in the extracted directory (including subdirectories)
    const shpFile = await findShapefileRecursively(inputDir);

    if (!shpFile) {
      throw new Error('No shapefile (.shp) found in the uploaded zip');
    }

    const shpPath = shpFile.fullPath;
    const baseName = path.basename(shpFile.fileName, '.shp');
    
    // Read the shapefile
    const features = [];
    await shapefile.open(shpPath)
      .then(source => source.read()
        .then(function log(result) {
          if (result.done) return;
          features.push(result.value);
          return source.read().then(log);
        }));

    // Sort features by area (SUP_HA) to identify central, buffer1, buffer2
    features.sort((a, b) => {
      const areaA = a.properties.SUP_HA || 0;
      const areaB = b.properties.SUP_HA || 0;
      return areaA - areaB;
    });

    // Assuming: smallest = central, middle = buffer1, largest = buffer2
    if (features.length !== 3) {
      throw new Error(`Expected 3 polygons, found ${features.length}`);
    }

    const [central, buffer1, buffer2] = features;

    // Create multi-part polygon collection
    const multiPartFeature = {
      type: 'Feature',
      geometry: {
        type: 'MultiPolygon',
        coordinates: []
      },
      properties: {
        // Preserve original properties from central polygon
        ...central.properties,
        // Add area fields for each part
        SUP_HA: central.properties.SUP_HA || 0,
        SUP_HA_2: buffer1.properties.SUP_HA || 0,
        SUP_HA_3: buffer2.properties.SUP_HA || 0,
        // Add metadata
        PART_COUNT: 3,
        CONVERSION_DATE: new Date().toISOString()
      }
    };

    // Add coordinates from each polygon to the multi-part geometry
    [central, buffer1, buffer2].forEach(feature => {
      if (feature.geometry.type === 'Polygon') {
        multiPartFeature.geometry.coordinates.push(feature.geometry.coordinates);
      } else if (feature.geometry.type === 'MultiPolygon') {
        feature.geometry.coordinates.forEach(coords => {
          multiPartFeature.geometry.coordinates.push(coords);
        });
      }
    });

    // Create FeatureCollection for output
    const outputCollection = {
      type: 'FeatureCollection',
      features: [multiPartFeature]
    };

    // Write the new shapefile
    const outputZip = await shpwrite.zip(outputCollection, {
      folder: baseName + '_multipart',
      types: {
        point: 'Point',
        polygon: 'MultiPolygon',
        line: 'LineString'
      }
    });

    // Save the output zip file
    const outputPath = path.join(outputDir, `${baseName}_multipart.zip`);
    await fs.writeFile(outputPath, Buffer.from(outputZip));

    return {
      success: true,
      outputPath,
      metadata: {
        originalFeatures: 3,
        outputFeatures: 1,
        areas: {
          central: multiPartFeature.properties.SUP_HA,
          buffer1: multiPartFeature.properties.SUP_HA_2,
          buffer2: multiPartFeature.properties.SUP_HA_3
        }
      }
    };

  } catch (error) {
    throw new Error(`Processing error: ${error.message}`);
  }
}

/**
 * Main API endpoint for shapefile conversion
 */
app.post('/api/convert', upload.single('shapefile'), async (req, res) => {
  const tempDir = path.join('uploads', `temp_${Date.now()}`);
  const outputDir = path.join('output', `output_${Date.now()}`);

  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    // Create temporary directories
    await fs.ensureDir(tempDir);
    await fs.ensureDir(outputDir);

    // Extract the uploaded zip file
    await fs.createReadStream(req.file.path)
      .pipe(unzipper.Extract({ path: tempDir }))
      .promise();

    // Process the shapefile
    const result = await processShapefile(tempDir, outputDir);

    // Send the output file
    res.download(result.outputPath, async (err) => {
      // Cleanup temporary files
      await fs.remove(req.file.path);
      await fs.remove(tempDir);
      
      // Schedule cleanup of output after download
      setTimeout(async () => {
        await fs.remove(outputDir);
      }, 60000); // Clean up after 1 minute
    });

  } catch (error) {
    console.error('Conversion error:', error);
    
    // Cleanup on error
    await fs.remove(req.file?.path).catch(() => {});
    await fs.remove(tempDir).catch(() => {});
    await fs.remove(outputDir).catch(() => {});

    res.status(500).json({ 
      error: 'Conversion failed', 
      details: error.message 
    });
  }
});

/**
 * Health check endpoint
 */
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'healthy',
    service: 'Shapefile Conversion API',
    version: '1.0.0'
  });
});

/**
 * Get conversion status/metadata
 */
app.post('/api/analyze', upload.single('shapefile'), async (req, res) => {
  const tempDir = path.join('uploads', `temp_${Date.now()}`);

  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    await fs.ensureDir(tempDir);

    // Extract and analyze without converting
    await fs.createReadStream(req.file.path)
      .pipe(unzipper.Extract({ path: tempDir }))
      .promise();

    // Find and read shapefile (including subdirectories)
    const shpFile = await findShapefileRecursively(tempDir);

    if (!shpFile) {
      throw new Error('No shapefile found');
    }

    const features = [];
    await shapefile.open(shpFile.fullPath)
      .then(source => source.read()
        .then(function log(result) {
          if (result.done) return;
          features.push(result.value);
          return source.read().then(log);
        }));

    // Analyze the features
    const analysis = {
      featureCount: features.length,
      features: features.map((f, i) => ({
        index: i,
        type: f.geometry.type,
        area_hectares: f.properties.SUP_HA,
        properties: Object.keys(f.properties)
      })),
      ready: features.length === 3
    };

    // Cleanup
    await fs.remove(req.file.path);
    await fs.remove(tempDir);

    res.json(analysis);

  } catch (error) {
    // Cleanup on error
    await fs.remove(req.file?.path).catch(() => {});
    await fs.remove(tempDir).catch(() => {});

    res.status(500).json({ 
      error: 'Analysis failed', 
      details: error.message 
    });
  }
});

// Start the server
app.listen(PORT, () => {
  console.log(`Shapefile Conversion API running on port ${PORT}`);
  console.log(`Endpoints:`);
  console.log(`  POST /api/convert - Convert shapefile to multipart`);
  console.log(`  POST /api/analyze - Analyze shapefile structure`);
  console.log(`  GET  /api/health  - Health check`);
});

module.exports = app;