# @turf/combine

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## combine

Combines a [FeatureCollection][1] of [Point][2], [LineString][3], or [Polygon][4] features
into [MultiPoint][5], [MultiLineString][6], or [MultiPolygon][7] features.

**Parameters**

-   `fc` **[FeatureCollection][8]&lt;([Point][9] \| [LineString][10] \| [Polygon][11])>** a FeatureCollection of any type

**Examples**

```javascript
var fc = turf.featureCollection([
  turf.point([19.026432, 47.49134]),
  turf.point([19.074497, 47.509548])
]);

var combined = turf.combine(fc);

//addToMap
var addToMap = [combined]
```

Returns **[FeatureCollection][8]&lt;([MultiPoint][12] \| [MultiLineString][13] \| [MultiPolygon][14])>** a FeatureCollection of corresponding type to input

[1]: https://tools.ietf.org/html/rfc7946#section-3.3

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.3

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[8]: https://tools.ietf.org/html/rfc7946#section-3.3

[9]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[10]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[11]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[12]: https://tools.ietf.org/html/rfc7946#section-3.1.3

[13]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[14]: https://tools.ietf.org/html/rfc7946#section-3.1.7

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/combine
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
