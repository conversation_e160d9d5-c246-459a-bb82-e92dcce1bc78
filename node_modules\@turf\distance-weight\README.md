# @turf/distance-weight

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## pNormDistance

calcualte the Minkowski p-norm distance between two features.

**Parameters**

-   `feature1`  point feature
-   `feature2`  point feature
-   `p`  p-norm 1=&lt;p&lt;=infinity 1: Manhattan distance 2: Euclidean distance

## distanceWeight

**Parameters**

-   `fc` **[FeatureCollection](https://tools.ietf.org/html/rfc7946#section-3.3)&lt;any>** FeatureCollection.
-   `options` **[Object](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object)?** option object.
    -   `options.threshold` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** If the distance between neighbor and
        target features is greater than threshold, the weight of that neighbor is 0. (optional, default `10000`)
    -   `options.p` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** Minkowski p-norm distance parameter.
        1: Manhattan distance. 2: Euclidean distance. 1=&lt;p&lt;=infinity. (optional, default `2`)
    -   `options.binary` **[boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** If true, weight=1 if d &lt;= threshold otherwise weight=0.
         If false, weight=Math.pow(d, alpha). (optional, default `false`)
    -   `options.alpha` **[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)** distance decay parameter.
        A big value means the weight decay quickly as distance increases. (optional, default `-1`)
    -   `options.standardization` **[boolean](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean)** row standardization. (optional, default `false`)

**Examples**

```javascript
var bbox = [-65, 40, -63, 42];
var dataset = turf.randomPoint(100, { bbox: bbox });
var result = turf.distanceWeight(dataset);
```

Returns **[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[Array](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array)&lt;[number](https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number)>>** distance weight matrix.

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/distance-weight
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
