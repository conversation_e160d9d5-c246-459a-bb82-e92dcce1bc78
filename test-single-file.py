"""
Simple test script for testing a single shapefile with the API
This demonstrates how to test just one specific file instead of batch processing
"""

import os
import sys

# Add the current directory to Python path to import the client
sys.path.append('.')

try:
    from python_client import ShapefileConverterClient
except ImportError:
    print("Error: Could not import ShapefileConverterClient")
    print("Make sure python-client.py is in the same directory")
    sys.exit(1)

def test_specific_file():
    """Test a specific shapefile"""
    
    # Specify the exact file you want to test
    test_file = r"C:\Users\<USER>\Downloads\Heron_test.zip"
    
    print(f"Testing single file: {os.path.basename(test_file)}")
    print("=" * 50)
    
    # Initialize client
    client = ShapefileConverterClient("http://localhost:3000")
    
    # Check if API is running
    if not client.health_check():
        print("❌ API is not running. Please start the Node.js server first.")
        print("Run: npm start")
        return False
    
    print("✅ API is running and healthy!")
    
    # Check if file exists
    if not os.path.exists(test_file):
        print(f"❌ File not found: {test_file}")
        return False
    
    print(f"✅ File found: {os.path.basename(test_file)}")
    
    # Step 1: Analyze the shapefile
    print(f"\n📊 Analyzing shapefile...")
    analysis = client.analyze_shapefile(test_file)
    
    if not analysis:
        print("❌ Analysis failed!")
        return False
    
    print("✅ Analysis successful!")
    print(f"   Features found: {analysis['featureCount']}")
    print(f"   Ready for conversion: {'Yes' if analysis['ready'] else 'No'}")
    
    # Show feature details
    for i, feature in enumerate(analysis['features']):
        area = feature.get('area_hectares', 'N/A')
        print(f"   Feature {i+1}: {feature['type']}, Area: {area} ha")
    
    # Step 2: Convert if ready
    if analysis['ready']:
        print(f"\n🔄 Converting shapefile...")
        output_file = f"output/{os.path.splitext(os.path.basename(test_file))[0]}_converted.zip"
        
        success = client.convert_to_multipart(
            input_path=test_file,
            output_path=output_file
        )
        
        if success:
            print("✅ Conversion successful!")
            print(f"   Output saved to: {output_file}")
            return True
        else:
            print("❌ Conversion failed!")
            return False
    else:
        print(f"\n⚠️  Skipping conversion - file is not ready")
        print(f"   Reason: Expected 3 polygons, found {analysis['featureCount']}")
        return False

if __name__ == "__main__":
    print("🧪 Single File Shapefile Converter Test")
    print("=" * 50)
    
    success = test_specific_file()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Test completed successfully!")
    else:
        print("❌ Test failed!")
    
    print("\n💡 To test a different file:")
    print("   Edit the 'test_file' variable in this script")
    print("   Available files:")
    print("   - Heron_test.zip")
    print("   - Habitats_fauniques.zip") 
    print("   - Habitats_fauniques (3).zip")
