# @turf/line-chunk

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineChunk

Divides a [LineString][1] into chunks of a specified length.
If the line is shorter than the segment length then the original line is returned.

**Parameters**

-   `geojson` **([FeatureCollection][2] \| [Geometry][3] \| [Feature][4]&lt;([LineString][5] \| [MultiLineString][6])>)** the lines to split
-   `segmentLength` **[number][7]** how long to make each segment
-   `options` **[Object][8]** Optional parameters (optional, default `{}`)
    -   `options.units` **[string][9]** units can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)
    -   `options.reverse` **[boolean][10]** reverses coordinates to start the first chunked segment at the end (optional, default `false`)

**Examples**

```javascript
var line = turf.lineString([[-95, 40], [-93, 45], [-85, 50]]);

var chunk = turf.lineChunk(line, 15, {units: 'miles'});

//addToMap
var addToMap = [chunk];
```

Returns **[FeatureCollection][2]&lt;[LineString][5]>** collection of line segments

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.3

[3]: https://tools.ietf.org/html/rfc7946#section-3.1

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[9]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[10]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/line-chunk
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
