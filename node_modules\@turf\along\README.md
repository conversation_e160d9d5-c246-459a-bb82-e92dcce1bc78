# @turf/along

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## along

Takes a [LineString][1] and returns a [Point][2] at a specified distance along the line.

**Parameters**

-   `line` **[Feature][3]&lt;[LineString][4]>** input line
-   `distance` **[number][5]** distance along the line
-   `options` **[Object][6]?** Optional parameters
    -   `options.units` **[string][7]** can be degrees, radians, miles, or kilometers (optional, default `"kilometers"`)

**Examples**

```javascript
var line = turf.lineString([[-83, 30], [-84, 36], [-78, 41]]);
var options = {units: 'miles'};

var along = turf.along(line, 200, options);

//addToMap
var addToMap = [along, line]
```

Returns **[Feature][3]&lt;[Point][8]>** Point `distance` `units` along the line

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[8]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/along
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
