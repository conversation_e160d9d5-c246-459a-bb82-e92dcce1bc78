# @turf/line-to-polygon

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineToPolygon

Converts (Multi)LineString(s) to Polygon(s).

**Parameters**

-   `lines` **([FeatureCollection][1] \| [Feature][2]&lt;([LineString][3] \| [MultiLineString][4])>)** Features to convert
-   `options` **[Object][5]** Optional parameters (optional, default `{}`)
    -   `options.properties` **[Object][5]** translates GeoJSON properties to Feature (optional, default `{}`)
    -   `options.autoComplete` **[boolean][6]** auto complete linestrings (matches first & last coordinates) (optional, default `true`)
    -   `options.orderCoords` **[boolean][6]** sorts linestrings to place outer ring at the first position of the coordinates (optional, default `true`)

**Examples**

```javascript
var line = turf.lineString([[125, -30], [145, -30], [145, -20], [125, -20], [125, -30]]);

var polygon = turf.lineToPolygon(line);

//addToMap
var addToMap = [polygon];
```

Returns **[Feature][2]&lt;([Polygon][7] \| [MultiPolygon][8])>** converted to Polygons

[1]: https://tools.ietf.org/html/rfc7946#section-3.3

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[8]: https://tools.ietf.org/html/rfc7946#section-3.1.7

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/line-to-polygon
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
