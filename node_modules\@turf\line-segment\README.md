# @turf/line-segment

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineSegment

Creates a [FeatureCollection][1] of 2-vertex [LineString][2] segments from a [(Multi)LineString][2] or [(Multi)Polygon][3].

**Parameters**

-   `geojson` **([Geometry][4] \| [FeatureCollection][5] \| [Feature][6]&lt;([LineString][7] \| [MultiLineString][8] \| [MultiPolygon][9] \| [Polygon][10])>)** GeoJSON Polygon or LineString

**Examples**

```javascript
var polygon = turf.polygon([[[-50, 5], [-40, -10], [-50, -10], [-40, 5], [-50, 5]]]);
var segments = turf.lineSegment(polygon);

//addToMap
var addToMap = [polygon, segments]
```

Returns **[FeatureCollection][5]&lt;[LineString][7]>** 2-vertex line segments

[1]: https://tools.ietf.org/html/rfc7946#section-3.3

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[4]: https://tools.ietf.org/html/rfc7946#section-3.1

[5]: https://tools.ietf.org/html/rfc7946#section-3.3

[6]: https://tools.ietf.org/html/rfc7946#section-3.2

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[8]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[9]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[10]: https://tools.ietf.org/html/rfc7946#section-3.1.6

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/line-segment
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
