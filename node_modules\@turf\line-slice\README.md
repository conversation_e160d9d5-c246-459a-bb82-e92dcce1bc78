# @turf/line-slice

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineSlice

Takes a [line][1], a start [Point][2], and a stop point
and returns a subsection of the line in-between those points.
The start & stop points don't need to fall exactly on the line.

This can be useful for extracting only the part of a route between waypoints.

**Parameters**

-   `startPt` **[Coord][3]** starting point
-   `stopPt` **[Coord][3]** stopping point
-   `line` **([Feature][4]&lt;[LineString][5]> | [LineString][5])** line to slice

**Examples**

```javascript
var line = turf.lineString([
    [-77.031669, 38.878605],
    [-77.029609, 38.881946],
    [-77.020339, 38.884084],
    [-77.025661, 38.885821],
    [-77.021884, 38.889563],
    [-77.019824, 38.892368]
]);
var start = turf.point([-77.029609, 38.881946]);
var stop = turf.point([-77.021884, 38.889563]);

var sliced = turf.lineSlice(start, stop, line);

//addToMap
var addToMap = [start, stop, line]
```

Returns **[Feature][4]&lt;[LineString][5]>** sliced line

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.4

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/line-slice
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
