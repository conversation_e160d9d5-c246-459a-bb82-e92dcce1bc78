# @turf/ellipse

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## ellipse

Takes a [Point][1] and calculates the ellipse polygon given two semi-axes expressed in variable units and steps for precision.

**Parameters**

-   `center` **[Coord][2]** center point
-   `xSemiAxis` **[number][3]** semi (major) axis of the ellipse along the x-axis
-   `ySemiAxis` **[number][3]** semi (minor) axis of the ellipse along the y-axis
-   `options` **[Object][4]** Optional parameters (optional, default `{}`)
    -   `options.angle` **[number][3]** angle of rotation (along the vertical axis), from North in decimal degrees, negative clockwise (optional, default `0`)
    -   `options.pivot` **[Coord][2]** point around which the rotation will be performed (optional, default `'origin'`)
    -   `options.steps` **[number][3]** number of steps (optional, default `64`)
    -   `options.units` **[string][5]** unit of measurement for axes (optional, default `'kilometers'`)
    -   `options.properties` **[Object][4]** properties (optional, default `{}`)

**Examples**

```javascript
var center = [-75, 40];
var xSemiAxis = 5;
var ySemiAxis = 2;
var ellipse = turf.ellipse(center, xSemiAxis, ySemiAxis);

//addToMap
var addToMap = [turf.point(center), ellipse]
```

Returns **[Feature][6]&lt;[Polygon][7]>** ellipse polygon

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[6]: https://tools.ietf.org/html/rfc7946#section-3.2

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.6

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/ellipse
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
