# @turf/difference

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## difference

Finds the difference between two [polygons][1] by clipping the second polygon from the first.

**Parameters**

-   `polygon1` **[Feature][2]&lt;([Polygon][3] \| [MultiPolygon][4])>** input Polygon feature
-   `polygon2` **[Feature][2]&lt;([Polygon][3] \| [MultiPolygon][4])>** Polygon feature to difference from polygon1

**Examples**

```javascript
var polygon1 = turf.polygon([[
  [128, -26],
  [141, -26],
  [141, -21],
  [128, -21],
  [128, -26]
]], {
  "fill": "#F00",
  "fill-opacity": 0.1
});
var polygon2 = turf.polygon([[
  [126, -28],
  [140, -28],
  [140, -20],
  [126, -20],
  [126, -28]
]], {
  "fill": "#00F",
  "fill-opacity": 0.1
});

var difference = turf.difference(polygon1, polygon2);

//addToMap
var addToMap = [polygon1, polygon2, difference];
```

Returns **([Feature][2]&lt;([Polygon][3] \| [MultiPolygon][4])> | null)** a Polygon or MultiPolygon feature showing the area of `polygon1` excluding the area of `polygon2` (if empty returns `null`)

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[2]: https://tools.ietf.org/html/rfc7946#section-3.2

[3]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.7

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/difference
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
