'use strict';

var polygonClipping = require('polygon-clipping');
var helpers = require('@turf/helpers');
var invariant = require('@turf/invariant');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var polygonClipping__default = /*#__PURE__*/_interopDefaultLegacy(polygonClipping);

/**
 * Finds the difference between two {@link Polygon|polygons} by clipping the second polygon from the first.
 *
 * @name difference
 * @param {Feature<Polygon|MultiPolygon>} polygon1 input Polygon feature
 * @param {Feature<Polygon|MultiPolygon>} polygon2 Polygon feature to difference from polygon1
 * @returns {Feature<Polygon|MultiPolygon>|null} a Polygon or MultiPolygon feature showing the area of `polygon1` excluding the area of `polygon2` (if empty returns `null`)
 * @example
 * var polygon1 = turf.polygon([[
 *   [128, -26],
 *   [141, -26],
 *   [141, -21],
 *   [128, -21],
 *   [128, -26]
 * ]], {
 *   "fill": "#F00",
 *   "fill-opacity": 0.1
 * });
 * var polygon2 = turf.polygon([[
 *   [126, -28],
 *   [140, -28],
 *   [140, -20],
 *   [126, -20],
 *   [126, -28]
 * ]], {
 *   "fill": "#00F",
 *   "fill-opacity": 0.1
 * });
 *
 * var difference = turf.difference(polygon1, polygon2);
 *
 * //addToMap
 * var addToMap = [polygon1, polygon2, difference];
 */
function difference(polygon1, polygon2) {
  var geom1 = invariant.getGeom(polygon1);
  var geom2 = invariant.getGeom(polygon2);
  var properties = polygon1.properties || {};

  var differenced = polygonClipping__default['default'].difference(
    geom1.coordinates,
    geom2.coordinates
  );
  if (differenced.length === 0) return null;
  if (differenced.length === 1) return helpers.polygon(differenced[0], properties);
  return helpers.multiPolygon(differenced, properties);
}

module.exports = difference;
module.exports.default = difference;
