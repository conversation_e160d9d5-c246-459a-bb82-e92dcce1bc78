# @turf/concave

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## concave

Takes a set of [points][1] and returns a concave hull Polygon or MultiPolygon.
Internally, this uses [turf-tin][2] to generate geometries.

**Parameters**

-   `points` **[FeatureCollection][3]&lt;[Point][4]>** input points
-   `options` **[Object][5]** Optional parameters (optional, default `{}`)
    -   `options.maxEdge` **[number][6]** the length (in 'units') of an edge necessary for part of the hull to become concave. (optional, default `Infinity`)
    -   `options.units` **[string][7]** can be degrees, radians, miles, or kilometers (optional, default `'kilometers'`)

**Examples**

```javascript
var points = turf.featureCollection([
  turf.point([-63.601226, 44.642643]),
  turf.point([-63.591442, 44.651436]),
  turf.point([-63.580799, 44.648749]),
  turf.point([-63.573589, 44.641788]),
  turf.point([-63.587665, 44.64533]),
  turf.point([-63.595218, 44.64765])
]);
var options = {units: 'miles', maxEdge: 1};

var hull = turf.concave(points, options);

//addToMap
var addToMap = [points, hull]
```

Returns **([Feature][8]&lt;([Polygon][9] \| [MultiPolygon][10])> | null)** a concave hull (null value is returned if unable to compute hull)

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[2]: https://github.com/Turfjs/turf-tin

[3]: https://tools.ietf.org/html/rfc7946#section-3.3

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.2

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[8]: https://tools.ietf.org/html/rfc7946#section-3.2

[9]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[10]: https://tools.ietf.org/html/rfc7946#section-3.1.7

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/concave
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
