# @turf/line-offset

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineOffset

Takes a [line][1] and returns a [line][1] at offset by the specified distance.

**Parameters**

-   `geojson` **([Geometry][2] \| [Feature][3]&lt;([LineString][4] \| [MultiLineString][5])>)** input GeoJSON
-   `distance` **[number][6]** distance to offset the line (can be of negative value)
-   `options` **[Object][7]** Optional parameters (optional, default `{}`)
    -   `options.units` **[string][8]** can be degrees, radians, miles, kilometers, inches, yards, meters (optional, default `'kilometers'`)

**Examples**

```javascript
var line = turf.lineString([[-83, 30], [-84, 36], [-78, 41]], { "stroke": "#F00" });

var offsetLine = turf.lineOffset(line, 2, {units: 'miles'});

//addToMap
var addToMap = [offsetLine, line]
offsetLine.properties.stroke = "#00F"
```

Returns **[Feature][3]&lt;([LineString][4] \| [MultiLineString][5])>** Line offset from the input line

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[2]: https://tools.ietf.org/html/rfc7946#section-3.1

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[7]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[8]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/line-offset
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
