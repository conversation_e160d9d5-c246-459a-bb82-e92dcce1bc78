{"name": "@turf/great-circle", "version": "6.5.0", "description": "turf great-circle module", "author": "Turf Authors", "contributors": ["<PERSON> <@springmeyer>", "<PERSON><PERSON> <@stepan<PERSON><PERSON><PERSON>>", "<PERSON> <@DenisCarriere>"], "license": "MIT", "bugs": {"url": "https://github.com/Turfjs/turf/issues"}, "homepage": "https://github.com/Turfjs/turf", "repository": {"type": "git", "url": "git://github.com/Turfjs/turf.git"}, "funding": "https://opencollective.com/turf", "publishConfig": {"access": "public"}, "keywords": ["turf", "arc", "line", "great", "circle"], "main": "dist/js/index.js", "module": "dist/es/index.js", "exports": {"./package.json": "./package.json", ".": {"import": "./dist/es/index.js", "require": "./dist/js/index.js"}}, "types": "index.d.ts", "sideEffects": false, "files": ["dist", "index.d.ts"], "scripts": {"bench": "node -r esm bench.js", "build": "rollup -c ../../rollup.config.js && echo '{\"type\":\"module\"}' > dist/es/package.json", "docs": "node ../../scripts/generate-readmes", "test": "npm-run-all test:*", "test:tape": "node -r esm test.js", "test:types": "tsc --esModuleInterop --noEmit types.ts"}, "devDependencies": {"@turf/truncate": "^6.5.0", "benchmark": "*", "load-json-file": "*", "npm-run-all": "*", "rollup": "*", "tape": "*", "write-json-file": "*"}, "dependencies": {"@turf/helpers": "^6.5.0", "@turf/invariant": "^6.5.0"}, "gitHead": "5375941072b90d489389db22b43bfe809d5e451e"}