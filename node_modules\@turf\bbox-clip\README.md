# @turf/bbox-clip

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## bboxClip

Takes a [Feature][1] and a bbox and clips the feature to the bbox using [lineclip][2].
May result in degenerate edges when clipping Polygons.

**Parameters**

-   `feature` **[Feature][3]&lt;([LineString][4] \| [MultiLineString][5] \| [Polygon][6] \| [MultiPolygon][7])>** feature to clip to the bbox
-   `bbox` **[BBox][8]** extent in [minX, minY, maxX, maxY] order

**Examples**

```javascript
var bbox = [0, 0, 10, 10];
var poly = turf.polygon([[[2, 2], [8, 4], [12, 8], [3, 7], [2, 2]]]);

var clipped = turf.bboxClip(poly, bbox);

//addToMap
var addToMap = [bbox, poly, clipped]
```

Returns **[Feature][3]&lt;([LineString][4] \| [MultiLineString][5] \| [Polygon][6] \| [MultiPolygon][7])>** clipped Feature

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://github.com/mapbox/lineclip

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.5

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[7]: https://tools.ietf.org/html/rfc7946#section-3.1.7

[8]: https://tools.ietf.org/html/rfc7946#section-5

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/bbox-clip
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
