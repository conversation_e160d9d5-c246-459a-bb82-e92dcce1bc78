# @turf/centroid

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## centroid

Takes one or more features and calculates the centroid using the mean of all vertices.
This lessens the effect of small islands and artifacts when calculating the centroid of a set of polygons.

**Parameters**

-   `g<PERSON><PERSON><PERSON>` **[GeoJSON][1]** GeoJSON to be centered
-   `options` **[Object][2]** Optional Parameters (optional, default `{}`)
    -   `options.properties` **[Object][2]** an Object that is used as the [Feature][3]'s properties (optional, default `{}`)

**Examples**

```javascript
var polygon = turf.polygon([[[-81, 41], [-88, 36], [-84, 31], [-80, 33], [-77, 39], [-81, 41]]]);

var centroid = turf.centroid(polygon);

//addToMap
var addToMap = [polygon, centroid]
```

Returns **[Feature][4]&lt;[Point][5]>** the centroid of the input features

[1]: https://tools.ietf.org/html/rfc7946#section-3

[2]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.2

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.2

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/centroid
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
