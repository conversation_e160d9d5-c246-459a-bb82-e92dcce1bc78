# @turf/line-arc

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## lineArc

Creates a circular arc, of a circle of the given radius and center point, between bearing1 and bearing2;
0 bearing is North of center point, positive clockwise.

**Parameters**

-   `center` **[Coord][1]** center point
-   `radius` **[number][2]** radius of the circle
-   `bearing1` **[number][2]** angle, in decimal degrees, of the first radius of the arc
-   `bearing2` **[number][2]** angle, in decimal degrees, of the second radius of the arc
-   `options` **[Object][3]** Optional parameters (optional, default `{}`)
    -   `options.steps` **[number][2]** number of steps (optional, default `64`)
    -   `options.units` **[string][4]** miles, kilometers, degrees, or radians (optional, default `'kilometers'`)

**Examples**

```javascript
var center = turf.point([-75, 40]);
var radius = 5;
var bearing1 = 25;
var bearing2 = 47;

var arc = turf.lineArc(center, radius, bearing1, bearing2);

//addToMap
var addToMap = [center, arc]
```

Returns **[Feature][5]&lt;[LineString][6]>** line arc

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[2]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/String

[5]: https://tools.ietf.org/html/rfc7946#section-3.2

[6]: https://tools.ietf.org/html/rfc7946#section-3.1.4

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/line-arc
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
