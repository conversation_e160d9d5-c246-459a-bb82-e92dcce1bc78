# @turf/boolean-clockwise

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## booleanClockwise

Takes a ring and return true or false whether or not the ring is clockwise or counter-clockwise.

**Parameters**

-   `line` **([Feature][1]&lt;[LineString][2]> | [LineString][2] \| [Array][3]&lt;[Array][3]&lt;[number][4]>>)** to be evaluated

**Examples**

```javascript
var clockwiseRing = turf.lineString([[0,0],[1,1],[1,0],[0,0]]);
var counterClockwiseRing = turf.lineString([[0,0],[1,0],[1,1],[0,0]]);

turf.booleanClockwise(clockwiseRing)
//=true
turf.booleanClockwise(counterClockwiseRing)
//=false
```

Returns **[boolean][5]** true/false

[1]: https://tools.ietf.org/html/rfc7946#section-3.2

[2]: https://tools.ietf.org/html/rfc7946#section-3.1.4

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[5]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/boolean-clockwise
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
