---
title: JSZip
layout: default
section: main
---

<div class="row">
<div class="col-md-5">

JSZip is a javascript library for creating, reading and editing .zip files, with a
lovely and simple API.

</div>
<div class="col-md-7">
<p>
  <strong>Current version</strong> : v2.2.0
</p>
<p>
  <strong>License</strong> : JSZip is dual-licensed. You may use it under the
  MIT license <em>or</em> the GPLv3 license. See
  <a href="https://github.com/Stuk/jszip/blob/master/LICENSE.markdown">LICENSE.markdown</a>.
</p>
</div>

<div class="row">
<div class="col-md-5">

<h3>Example</h3>

<script type="text/javascript">
imgData = "R0lGODdhBQAFAIACAAAAAP/eACwAAAAABQAFAAACCIwPkWerClIBADs=";

jQuery(function($) {
  $("#demo").click(function () {
      try {
          eval($("#demo-code").val());
          $("#status")
          .removeClass()
          .addClass("text-success")
          .text("Done!");
      }
      catch (e) {
          $("#status")
          .removeClass()
          .addClass("text-danger")
          .text(e);
      }
  });
});

</script>
<textarea class="form-control" id="demo-code" rows="7" spellcheck="false">
var zip = new JSZip();
zip.file("Hello.txt", "Hello World\n");
var img = zip.folder("images");
img.file("smile.gif", imgData, {base64: true});
<<<<<<< HEAD
var content = zip.generate();
location.href="data:application/zip;base64,"+content;
      </textarea>
      <button id="demo">Run!</button>
      <span id="status"></span>
   </div>

   <div id="why" class="grid_4 border">
      <h2>Why?</h2>
      <ol>
      <li>JavaScript today is capable of generating a lot of data. The easiest way to deliver multiple files to your users is in a zip file. Instead of wasting server resources and bandwidth you can get the client to do it for you.</li>
      <li>Because it's cool!</li>
      </ol>
   </div>

   <div id="where" class="grid_3">
      <h2>Where?</h2>
      <p>
      <a href="http://github.com/Stuk/jszip/zipball/master" class="download grid_3 alpha omega">Download</a>
      <small>from <a href="http://github.com/Stuk/jszip">Github</a></small>
      </p>

      <p>
      See also: the <a href="test/">test suite</a>
      </p>
   </div>
   <hr/>
   <div class="grid_9 border">
      <h2>Tell me more!</h2>

      <h3>Browser support</h3>
      <table cellspacing="0" class="browser_support">
         <tr><th class="op">Opera</th> <th class="ff">Firefox</th> <th class="sf">Safari</th> <th class="cr">Chrome</th> <th class="ie">Internet Explorer</th> </tr>
         <tr><td>Yes</td> <td>Yes</td> <td>Yes</td> <td>Yes</td> <td>Yes</td></tr>
         <tr>
          <td>Tested with the latest version</td>
          <td>Tested with 3.0 / 3.6 / latest version</td>
          <td>Tested with the latest version</td>
          <td>Tested with the latest version</td>
          <td>Tested with IE 6 / 7 / 8 / 9 / 10</td>
         </tr>
      </table>

      <p>While JSZip should work everywhere, the tricky part is to give the zip file to the user.</p>

      <h3>Browser support for <a href="https://en.wikipedia.org/wiki/Data_URI_scheme">data URI scheme</a> with zip</h3>
      <table cellspacing="0" class="browser_support">
         <tr><th class="op">Opera</th> <th class="ff">Firefox</th> <th class="sf">Safari</th> <th class="cr">Chrome</th> <th class="ie">Internet Explorer</th> </tr>
         <tr><td>7.5+</td> <td>3.0+</td> <td>Yes</td> <td>Yes</td> <td>No</td></tr>
         <tr>
          <td>Filename is "default.zip"</td>
          <td>Filename is random alphanumeric with ".part" extension</td>
          <td>Filename is "Unknown" (no extension)</td>
          <td>Filename is "download.zip" on OSX and Linux, and just "download" on Windows (<a href="https://github.com/Stuk/jszip/issues/9">issue #9</a>)</td>
          <td><a href="http://msdn.microsoft.com/en-us/library/cc848897(VS.85).aspx">Only supports data URLs for some content</a>. (May be able to use <a href="http://www.phpied.com/mhtml-when-you-need-data-uris-in-ie7-and-under/">MHTML</a>?)</td>
         </tr>
      </table>

      <h3>Filename problems</h3>
	  <p>The biggest issue with JSZip is that the filenames are very awkward, Firefox generates filenames such as <code>a5sZQRsx.zip.part</code> (see bugs <a href="https://bugzilla.mozilla.org/show_bug.cgi?id=367231">367231</a> and <a href="https://bugzilla.mozilla.org/show_bug.cgi?id=532230">532230</a>), and Safari isn't much better with just <code>Unknown</code>. Sadly there is no pure Javascript solution (and working in every browsers) to this. However...</p>

      <h4>Solution-ish: <a href="https://github.com/dcneiner/downloadify">Downloadify</a></h4>
      <p>Downloadify uses a small Flash SWF to download files to a user's computer with a filename that you can choose. Doug Neiner has added the <code>dataType</code> option to allow you to pass a zip for downloading. Follow the <a href="http://pixelgraphics.us/downloadify/test.html">Downloadify demo</a> with the following changes:</p>
      <pre class="example">
zip = new JSZip();
zip.file("Hello.", "hello.txt");
Downloadify.create('downloadify',{
...
  data: function(){
    return zip.generate();
  },
...
  dataType: 'base64'
});</pre>

      <h4>Other solution-ish: Blob URL / FileSaver / FileSaver.js</h4>
      <p>With <a href="http://caniuse.com/bloburls">some recent browsers</a> come a new way to download Blobs (a zip file for example) : blob urls. The <a href="http://updates.html5rocks.com/2011/08/Downloading-resources-in-HTML5-a-download">download attribute on &lt;a&gt;</a> allows you to give the name of the file. Blob urls start to be widely supported but this attribute is currently only supported in Chrome and Firefox (>= 20). See <a href="examples/download-zip-file.html">the example</a>. Note : on IE 10/11, using a blob url as a href doesn't seem to work.</p>
      <pre class="example">
var blob = zip.generate({type:"blob"});
myLink.href = window.URL.createObjectURL(blob);
myLink.download = "myFile.zip";</pre>

      <p>An other solution is the <a href="http://www.w3.org/TR/file-writer-api/#the-filesaver-interface">FileSaver</a> interface, created (at the time of writing) with <code>saveAs</code>. This works on Chrome and IE &gt;= 10 (not in compatibility view) but not Firefox.</p>
      <pre class="example">
var blob = zip.generate({type:"blob"});
window.saveAs(blob, "hello.zip");</pre>

      <p>Finally, you can use the polyfill <a href="https://github.com/eligrey/FileSaver.js">FileSaver.js</a> which will use a FileSaver if present or else a blob url.</p>

      <h3>Usage with Google Gears</h3>
      <p>
      <a href="http://www.picurl.org/blog/author/franz/">Franz Buchinger</a> has written a brilliant tutorial on <a href="http://www.picurl.org/blog/2009/11/22/creating-zip-archives-with-gears/">using JSZip with Google Gears</a> (<a href="http://www.picurl.org/blog/2009/11/29/gearszipper-part2-adding-support-for-real-files-and-canvas-elements/">part 2</a>). If you want to let your Gears users download several files at once I really recommend having a look at some of his <a href="http://picurl.org/gears/zipper/">examples</a>.</p>

      <h3>Reading a zip file from an ajax call</h3>
      <p>
      When doing an ajax call to get the binary data, the browser will try to interpret the binary as text, corrupting it. The solution is to <a href="https://developer.mozilla.org/En/XMLHttpRequest/Using_XMLHttpRequest#Receiving_binary_data_in_older_browsers">set the mimetype to 'text/plain; charset=x-user-defined'</a>. This solution works well in all browsers but IE. If you need IE support, please see what is done in the file test/index.html.
      </p>
      <p>An other solution is to use a modern browser (supporting <a href="http://caniuse.com/xhr2">xhr2</a>) : setting <code>xhr.type = 'arraybuffer';</code> will do the trick, JSZip supports ArrayBuffers. Please see <a href="examples/get-binary-files-xhr2.html">the example</a>.

      <h3>Reading a local zip file (File API)</h3>
      <p>
      JSZip supports (if available in the browser) the File API : reading a local zip file is simple : <code>new JSZip(readerEvent.target.result);</code>. Please see the <a href="examples/read-local-file-api.html">complete example</a> for more details.
      </p>

      <h2 style="margin-top: 2em;">Documentation</h2>

      <h4 id="doc_new_JSZip">new JSZip() or JSZip()</h4>
      <dl>
        <dt>Description : </dt>
        <dd>The default constructor.</dd>
        <dt>Returns : </dt>
        <dd>A new JSZip.</dd>
      </dl>
      <pre class="example">
var zip = new JSZip();
// same as
var zip = JSZip();</pre>

      <h4 id="doc_new_JSZip_data_options">new JSZip(data [,options]) or JSZip(data [,options])</h4>
      <dl>
        <dt>Description : </dt>
        <dd>Create a new JSZip file and load an existing zip file. See the documentation of <a href="#doc_load_data_options"><code>load()</code></a> for more details and <a href="#zip_load_limits">this</a> for the limitations.</dd>
        <dt>Parameters : </dt>
        <dd><code>data</code> (same types as <a href="#doc_load_data_options"><code>load()</code></a>) the content of the zip file to load.</dd>
        <dd><code>options</code> (Object) options to pass to the <a href="#doc_load_data_options"><code>load()</code></a> method..</dd>
        <dt>Returns : </dt>
        <dd>A new JSZip.</dd>
      </dl>
      <pre class="example">
new JSZip(zipDataFromXHR, {base64:false});
// same as
JSZip(zipDataFromXHR, {base64:false});
// same as
var zip = new JSZip();
zip.load(zipDataFromXHR, {base64:false});</pre>

      <h4 id="doc_file_name">file(name)</h4>
      <dl>
        <dt>Description : </dt>
        <dd>Get a file with the specified name.</dd>
        <dt>Parameters : </dt>
        <dd><code>name</code> (String) the name of the file.</dd>
        <dt>Returns : </dt>
        <dd>The file if any, <code>null</code> otherwise. The file has the following structure :
          <ul>
            <li><code>name</code> the absolute path of the file</li>
            <li><code>options</code> the options of the file. The available options are :
              <ul>
                <li><code>base64</code>, boolean, cf <a href="#doc_file_name_data_options">file(name, data [,options])</a></li>
                <li><code>binary</code>, boolean, cf <a href="#doc_file_name_data_options">file(name, data [,options])</a></li>
                <li><code>dir</code>, boolean, true if this is a directory</li>
                <li><code>date</code>, date, cf <a href="#doc_file_name_data_options">file(name, data [,options])</a></li>
                <li><code>compression</code>, String, cf <a href="#doc_file_name_data_options">file(name, data [,options])</a></li>
              </ul>
            </li>
            <li><code>asText()</code>, string, the content as an utf8 string (utf8 encoded if necessary).</li>
            <li><code>asBinary()</code>, string, the content as binary (utf8 decoded if necessary).</li>
            <li><code>asArrayBuffer()</code>, ArrayBuffer, need a <a href="#jszip_support">compatible browser</a>.</li>
            <li><code>asUint8Array()</code>, Uint8Array, need a <a href="#jszip_support">compatible browser</a>.</li>
            <li><code>asNodeBuffer()</code>, nodejs Buffer, need <a href="#jszip_support">nodejs</a>.</li>
          </ul>
        </dd>
      </dl>
      <pre class="example">
var zip = new JSZip();
zip.file("file.txt", "content");

zip.file("file.txt").name // "file.txt"
zip.file("file.txt").asText() // "content"
zip.file("file.txt").options.dir // false

// utf8 example
var zip = new JSZip(zipFromAjaxWithUTF8);
zip.file("amount.txt").asText() // "&euro;15"
zip.file("amount.txt").asArrayBuffer() // an ArrayBuffer containing &euro;15
zip.file("amount.txt").asUint8Array() // an Uint8Array containing &euro;15
</pre>

      <h4 id="doc_file_regex">file(regex)</h4>
      <dl>
        <dt>Description : </dt>
        <dd>Search a file in the current folder and subfolders with a <a href="http://www.javascriptkit.com/javatutors/redev.shtml">regular expression</a>. The regex is tested against the relative filename.</dd>
        <dt>Parameters : </dt>
        <dd><code>regex</code> (RegExp) the regex to use.</dd>
        <dt>Returns : </dt>
        <dd>An array of matching files (an empty array if none matched).</dd>
      </dl>
      <pre class="example">
var zip = new JSZip();
zip.file("file1.txt", "content");
zip.file("file2.txt", "content");

zip.file(/file/); // array of size 2

// example with a relative path :
var folder = zip.folder("sub");
folder
  .file("file3.txt", "content")  // relative path from folder : file3.txt
  .file("file4.txt", "content"); // relative path from folder : file4.txt

folder.file(/file/);  // array of size 2
folder.file(/^file/); // array of size 2, the relative paths start with file

// arrays contain objects in the form:
// {name: "file2.txt", dir: false, asText : function () {...}, ...}</pre>

      <h4 id="doc_file_name_data_options">file(name, data [,options])</h4>
      <dl>
        <dt>Description : </dt>
        <dd>Add a file to the zip file.</dd>
        <dt>Parameters : </dt>
        <dd><code>name</code> (String) the name of the file.</dd>
        <dd><code>data</code> (String/ArrayBuffer/Uint8Array/Buffer) the content of the file.</dd>
        <dd><code>options</code> (Object) the options :
          <ul>
             <li><code>base64</code> (boolean) set to <code>true</code> if the data
             is base64 encoded. For example image data from a <code>&lt;canvas&gt;</code> element.
             Plain text and HTML do not need this option.</li>
             <li><code>binary</code> (boolean) defaults to <code>true</code> if the data
             is base64 encoded, <code>false</code> otherwise. If set to false then
             UTF-8 characters will be encoded. If the data is an ArrayBuffer or an Uint8Array, this will be set to true.</li>
             <li><code>date</code> (Date) use it to specify the last modification date.
             If not set the current date is used.</li>
             <li><code>compression</code> (String), default null. If set, specifies the file compression method to use. If not, the default file compression is used, cf <a href="#doc_generate_options">generate(options)</a>.</li>
             <li><code>optimizedBinaryString</code> (boolean), default false. Set it to true if (and only if) the input is a string and
             has already been prepared with a 0xFF mask.</li>
          </ul>
        </dd>
        <dt>Returns : </dt>
        <dd>A JSZip object, for chaining.</dd>
      </dl>
      <pre class="example">
zip.file("Hello.txt", "Hello World\n");
zip.file("smile.gif", "R0lGODdhBQAFAIACAAAAAP/eACwAAAAABQAFAAACCIwPkWerClIBADs=", {base64: true});
zip.file("magic.txt", "U2VjcmV0IGNvZGU=", {base64: true, binary: false});
zip.file("Xmas.txt", "Ho ho ho !", {date : new Date("December 25, 2007 00:00:01")});
zip.file("folder/file.txt", "file in folder");

zip.file("animals.txt", "dog,platypus\n").file("people.txt", "james,sebastian\n");

// result : Hello.txt, smile.gif, magic.txt, Xmas.txt, animals.txt, people.txt,
// folder/, folder/file.txt</pre>

      <h4 id="doc_folder_name">folder(name)</h4>
      <dl>
        <dt>Description : </dt>
        <dd>Add a directory to the zip file.</dd>
        <dt>Parameters : </dt>
        <dd><code>name</code> (String) the name of the directory.</dd>
        <dt>Returns : </dt>
        <dd>a new JSZip (for chaining), with the new folder as root.</dd>
      </dl>
      <pre class="example">
zip.folder("images");
zip.folder("css").file("style.css", "body {background: #FF0000}");
// or specify an absolute path (using forward slashes)
zip.file("css/font.css", "body {font-family: sans-serif}")

// result : images/, css/, css/style.css, css/font.css</pre>

      <h4 id="doc_folder_regex">folder(regex)</h4>
      <dl>
        <dt>Description : </dt>
        <dd>Search a subdirectory.</dd>
        <dd>Search a subdirectory in the current directory with a <a href="http://www.javascriptkit.com/javatutors/redev.shtml">regular expression</a>. The regex is tested against the relative path.</dd>
        <dt>Parameters : </dt>
        <dd><code>regex</code> (RegExp) the regex to use.</dd>
        <dt>Returns : </dt>
        <dd>An array of matching folders (an empty array if none matched).</dd>
      </dl>
      <pre class="example">
var zip = new JSZip();
zip.folder("home/Pierre/videos");
zip.folder("home/Pierre/photos");
zip.folder("home/Jean/videos");
zip.folder("home/Jean/photos");

zip.folder(/videos/); // array of size 2

zip.folder("home/Jean").folder(/^vid/); // array of 1</pre>

      <h4 id="doc_remove_name">remove(name)</h4>
      <p>Delete a file or folder.</p>
      <dl>
        <dt>Description : </dt>
        <dd>Delete a file or folder (recursively).</dd>
        <dt>Parameters : </dt>
        <dd><code>name</code> (String) the name of the file/folder to delete.</dd>
        <dt>Returns : </dt>
        <dd>The current JSZip object.</dd>
      </dl>
      <pre class="example">
var zip = new JSZip();
zip.file("Hello.txt", "Hello World\n");
zip.file("temp.txt", "nothing").remove("temp.txt");
// result : Hello.txt

zip.folder("css").file("style.css", "body {background: #FF0000}");
zip.remove("Hello.txt").remove("css");
//result : empty zip</pre>

      <h4 id="doc_generate_options">generate(options)</h4>
      <dl>
        <dt>Description : </dt>
        <dd>Generates the complete zip file.</dd>
        <dt>Parameters : </dt>
        <dd><code>options</code> (Object) the options to generate the zip file :
          <ul>
            <li><code>base64</code> (boolean) <strong>deprecated</strong>, use "type" instead. <code>false</code> to get the result as a raw byte string.
             Default : <code>true</code>, encode as base64.</li>
             <li><code>compression</code> (String) the default file compression method to use. <code>"STORE"</code> (no compression) by default,
             you can use <code>"DEFLATE"</code> or write your own.</li>
             <li><code>type</code> (String) the type of zip to return. The possible values are :
               <ul>
                  <li><code>base64</code> (default) : the result will be a string, the binary in a base64 form.</li>
                  <li><code>string</code> : the result will be a string in "binary" form, 1 byte per char.</li>
                  <li><code>uint8array</code> : the result will be a Uint8Array containing the zip. This requires a compatible browser.</li>
                  <li><code>arraybuffer</code> : the result will be a ArrayBuffer containing the zip. This requires a compatible browser.</li>
                  <li><code>blob</code> : the result will be a Blob containing the zip. This requires a compatible browser.</li>
                  <li><code>nodebuffer</code> : the result will be a nodejs Buffer containing the zip. This requires nodejs.</li>
               </ul>
             </li>
          </ul>
        </dd>
        <dt>Returns : </dt>
        <dd>The generated zip file.</dd>
      </dl>
      HTML5 note : when using type = "uint8array", "arraybuffer" or "blob", be sure to check if the browser supports it (you can use <a href="#jszip_support"><code>JSZip.support</code></a>).
      This method will throw an exception otherwise.
      <pre class="example">
content = zip.generate(); // base64
location.href="data:application/zip;base64,"+content;</pre>

      <pre class="example">
var blobLink = document.getElementById('blobLink');
blobLink.download = "hello.zip";
blobLink.href = window.URL.createObjectURL(
   zip.generate({type:"blob"})
);</pre>

      <pre class="example">
content = zip.generate({type:"string"});
for (var c = 0; c &lt; content.length; c++) {
    console.log(content.charCodeAt(c));
    // do other things
}</pre>

      <h4 id="doc_load_data_options">load(data, options)</h4>
      <dl>
        <dt>Description : </dt>
        <dd>Read an existing zip and merge the data in the current JSZip object. This technique has some limitations, see <a href="#zip_load_limits">below</a>.</dd>
        <dt>Parameters : </dt>
        <dd><code>data</code> (String/ArrayBuffer/Uint8Array/Buffer) the zip file</dd>
        <dd><code>options</code> (Object) the options to load the zip file :
          <ul>
            <li><code>base64</code> (boolean) <code>true</code> if the data is base64 encoded, <code>false</code> for binary. Default : <code>false</code>.</li>
            <li><code>checkCRC32</code> (boolean) <code>true</code> if the read data should be checked against its CRC32. Default : <code>false</code>.</li>
            <li><code>optimizedBinaryString</code> (boolean), default false. Set it to true if (and only if) the input is a string and
            has already been prepared with a 0xFF mask.</li>
          </ul>
        </dd>
        <dt>Returns : </dt>
        <dd>The current JSZip object.</dd>
      </dl>
      <pre class="example">
var zip = new JSZip();
zip.load(zipDataFromXHR);</pre>

      <h5>Zip features supported by this method</h5>
      <ul>
         <li>Compression (<code>DEFLATE</code> supported)</li>
         <li>zip with data descriptor</li>
         <li>ZIP64</li>
         <li>UTF8 in file name, UTF8 in file content</li>
      </ul>
      <h5>Zip features not (yet) supported</h5>
      <ul>
         <li>password protected zip</li>
         <li>multi-volume zip</li>
      </ul>

      <h4 id="doc_filter_predicate">filter(predicate)</h4>
      <dl>
        <dt>Description : </dt>
        <dd>Filter nested files/folders with the specified function.</dd>
        <dt>Parameters : </dt>
        <dd><code>predicate</code> (function) the predicate to use : <code>function (relativePath, file) {...}</code> It takes 2 arguments : the relative path and the file.
          <ul>
            <li><code>relativePath</code> (String) The filename and its path, reliatively to the current folder.</li>
            <li><code>file</code> (Object) The file being tested. Like the result of <a href="#doc_file_name"><code>file(name)</code></a>, the file has the form <code>{name:"...", options:{...}, asText:function,...}</code>.</li>
            <li>Return true if the file should be included, false otherwise.</li>
          </ul>
        </dd>
        <dt>Returns : </dt>
        <dd>An array of matching elements.</dd>
      </dl>
      <pre class="example">
var zip = new JSZip().folder("dir");
zip.file("readme.txt", "content");
zip.filter(function (relativePath, file){
  // relativePath == "readme.txt"
  // file = {name:"dir/readme.txt",options:{...},asText:function}
  return true/false;
});</pre>

      <h4 id="jszip_support">JSZip.support</h4>
      <p>
         If the browser supports them, JSZip can take advantage of some new features : ArrayBuffer, Blob, Uint8Array.
         To know if JSZip can use them, you can check the JSZip.support object. It contains the following
         properties :
         <ul>
            <li><code>arraybuffer</code> : true if JSZip can read and generate ArrayBuffer, false otherwise.</li>
            <li><code>uint8array</code> : true if JSZip can read and generate Uint8Array, false otherwise.</li>
            <li><code>blob</code> : true if JSZip can read and generate Blob, false otherwise.</li>
            <li><code>nodebuffer</code> : true if JSZip can read and generate nodejs Buffer, false otherwise.</li>
         </ul>
      </p>

      <h3 id="zip_load_limits">Loading zip files, limitations</h3>
      <h4>Not supported features</h4>
      <p>
      All the features of zip files are not supported.
      Classic zip files will work but encrypted zip, multi-volume, etc are not supported
      and the load() method will throw an <code>Error</code>.
      </p>
      <h4>ZIP64 and 32bit integers</h4>
      <p>
      ZIP64 files can be loaded, but only if the zip file is not "too big". ZIP64 uses 64bits integers
      but Javascript represents all numbers as
      <a href="http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-262.pdf">
         64-bit double precision IEEE 754 floating point numbers</a> (see section 8.5).
      So, we have 53bits for integers and
      <a href="https://developer.mozilla.org/en-US/docs/JavaScript/Reference/Operators/Bitwise_Operators">
         bitwise operations treat everything as 32bits</a>.
      So if all the 64bits integers can fit into 32 bits integers, everything will be fine. If
      it's not the case, you will have other problems anyway (see next limitation).
      </p>
      <h4>Performance issues</h4>
      <p>
      An other limitation comes from the browser (and the machine running the browser).
      A compressed zip file of 10MB is "easily" opened by firefox/chrome/opera/IE10 but
      will crash older IE. Also keep in mind that strings in javascript are encoded in UTF-16 :
      a 10MB ascii text file will take 20MB of memory.
      </p>
      <p>
      If you're having performance issues, please consider the following :
      <ul>
         <li>Don't use IE &lt; 10. Everything is better with WebGL support.</li>
         <li>
            If you load the file from an ajax call, ask your XHR an ArrayBuffer.
            Loading a string is asking for troubles.
         </li>
         <li>
            If you want to get the content of an ASCII file as a string, consider using
            <code>asBinary()</code> instead of <code>asText()</code>. The transformation
            "binary string" -&gt; "utf string" is a consuming process.
         </li>
      </ul>
      </p>
      <h4>The output zip will differ from the input zip</h4>
      <p>
      Reading and generating a zip file won't give you back the same file.
      Some data are discarded (file metadata) and other are added (subfolders).
      </p>

      <h2><a href="https://github.com/Stuk/jszip/blob/master/CHANGES.md">Changelog</a></h2>

      <h2 id="migrating_guide">Migrating Guide</h2>
      <h3>From 2.0.0 to 2.1.0</h3>
      <p>
         <ul>
            <li>The packaging changed : instead of loading jszip.js, jszip-load.js, jszip-inflate.js, jszip-deflate.js, just include dist/jszip.js or dist/jszip.min.js</li>
         </ul>
      </p>
      <h3>From 1.x to 2.x</h3>
      <p>
         <ul>
            <li><code>JSZipBase64</code> has been renamed to <code>JSZip.base64</code>.</li>
            <li>
               The <code>data</code> attribute doesn't exist anymore :
               use the getters <code>asText()</code>, <code>asBinary()</code>, etc
            </li>
            <li>
               The compression/decompression methods now give their input type with the
               <code>compressInputType</code> and <code>uncompressInputType</code> attributes.
         </ul>

         Example for the data attribute :
         <pre>// before
zip.file("test.txt").data;
zip.files["test.txt"].data;
zip.file("image.png").data;
zip.files["image.png"].data;

// after
zip.file("test.txt").asText();
zip.files["test.txt"].asText();
zip.file("image.png").asBinary();
zip.files["image.png"].asBinary();
      </p>
   </div>

   <div id="who" class="grid_3">
      <h2>Anything else?</h2>

      <h3>License</h3>
      <p>
      <a href="https://github.com/Stuk/jszip/blob/master/LICENSE.markdown">MIT license or GPLv3</a>
      </p>

      <h3>Version</h3>
      <p>2.2.1</p>
      <p>See the <a href="#migrating_guide">migrating guide</a> when updating the library!</p>

      <h3>Todo</h3>
      <ul>
       <li>Set correct version needed to extract</li>
       <li>Set correct version made by</li>
       <li>Set internal and external file attributes in central directory</li>
       <li>Allow setting of attributes/other info in <code>add()</code> options object</li>
       <li>Look into using <a href="http://www.phpied.com/mhtml-when-you-need-data-uris-in-ie7-and-under/">MHTML</a> for IE</li>
      </ul>

      <h3>Who?</h3>
      <p><a href="http://stuartk.com">Stuart Knightley</a>, with contributions from:</p>
      <ul>
       <li><a href="http://www.picurl.org/blog/author/franz/">Franz Buchinger</a></li>
       <li><a href="http://www.aadsm.net/">António Afonso</a></li>
       <li><a href="http://github.com/dduponchel">David Duponchel</a></li>
       <li><a href="http://yiminghe.javaeye.com">yiminghe</a></li>
      </ul>

      <h3>Contact and bugs</h3>
      <ul>
         <li><a href="https://github.com/Stuk/jszip/issues">Issue tracker</a></li>
         <li><a href="mailto:stuart -at- stuartk.com?subject=JSZip">Email me</a></li>
      </ul>
   </div>
=======
var content = zip.generate({type:"blob"});
// see FileSaver.js
saveAs(content, "example.zip");</textarea>
<button id="demo" class="btn btn-primary">Run!</button>
<span id="status"></span>
>>>>>>> Rewrite the documentation

</div>
<div class="col-md-7">

<h3>Installation</h3>

<p>
  <strong>With npm</strong> : <code>npm install jszip</code>
</p>
<p>
  <strong>With bower</strong> : <code>bower install Stuk/jszip</code>
</p>
<p>
  <strong>With component</strong> : <code>component install Stuk/jszip</code>
</p>
<p>
  <strong>Manually</strong> : <a href="http://github.com/Stuk/jszip/zipball/master">download JSZip</a>
  and include the file <code>dist/jszip.js</code> or <code>dist/jszip.min.js</code>
</p>
<br>
<p>
  Installed ? Great ! You can now check our
  <a href="{{site.baseurl}}/documentation/examples.html">guides and examples !</a>
</p>
</div>
</div>

<h3>Support</h3>

<table class="browser_support">
  <tr>
    <th class="support_op">Opera</th>
    <th class="support_ff">Firefox</th>
    <th class="support_sf">Safari</th>
    <th class="support_cr">Chrome</th>
    <th class="support_ie">Internet Explorer</th>
    <th class="support_nd">Node.js</th>
  </tr>
  <tr>
    <td>Yes</td>
    <td>Yes</td>
    <td>Yes</td>
    <td>Yes</td>
    <td>Yes</td>
    <td>Yes</td>
  </tr>
  <tr>
    <td>Tested with the latest version</td>
    <td>Tested with 3.0 / 3.6 / latest version</td>
    <td>Tested with the latest version</td>
    <td>Tested with the latest version</td>
    <td>Tested with IE 6 / 7 / 8 / 9 / 10</td>
    <td>Tested with node.js 0.8 and 0.10</td>
  </tr>
</table>

<h3>Getting help</h3>

<p>
Having trouble ? We'd like to help !
</p>
<ul>
  <li>
  Try the <a href="{{site.baseurl}}/documentation/faq.html">FAQ</a>, it has
  answers to common questions.
  </li>
  <li>
  If you're looking for informations about a specific method, try the
  <a href="{{site.baseurl}}/documentation/api_jszip.html">documentation</a>.
  </li>
  <li>
  Check the
  <a href="{{site.baseurl}}/documentation/examples.html">examples</a>.
  </li>
  <li>
  Report bugs in our
  <a href="https://github.com/Stuk/jszip/issues">Bug tracker</a>.
  </li>
</ul>

<h3>Test status</h3>

<dl class="dl-horizontal">
  <dt>Travis build :</dt>
  <dd>
  <a href="http://travis-ci.org/Stuk/jszip">
    <img src="https://secure.travis-ci.org/Stuk/jszip.png?branch=master" alt="" />
  </a>
  </dd>
  <dt>Saucelabs build :</dt>
  <dd>
  <a href="https://saucelabs.com/u/jszip">
    <img src="https://saucelabs.com/browser-matrix/jszip.svg" alt="" />
  </a>
  </dd>
  <dt>Live tests :</dt>
  <dd>
  <a href="{{site.baseurl}}/test/">See for yourself !</a>
  </dd>
</dl>
