# @turf/envelope

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## envelope

Takes any number of features and returns a rectangular [Polygon][1] that encompasses all vertices.

**Parameters**

-   `geojson` **[GeoJSON][2]** input features

**Examples**

```javascript
var features = turf.featureCollection([
  turf.point([-75.343, 39.984], {"name": "Location A"}),
  turf.point([-75.833, 39.284], {"name": "Location B"}),
  turf.point([-75.534, 39.123], {"name": "Location C"})
]);

var enveloped = turf.envelope(features);

//addToMap
var addToMap = [features, enveloped];
```

Returns **[Feature][3]&lt;[Polygon][4]>** a rectangular Polygon feature that encompasses all vertices

[1]: https://tools.ietf.org/html/rfc7946#section-3.1.6

[2]: https://tools.ietf.org/html/rfc7946#section-3

[3]: https://tools.ietf.org/html/rfc7946#section-3.2

[4]: https://tools.ietf.org/html/rfc7946#section-3.1.6

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/envelope
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
