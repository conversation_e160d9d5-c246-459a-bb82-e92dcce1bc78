#!/bin/bash

# Simple bash script to test the API with a single file
# This avoids Python dependency issues

echo "🧪 Testing Shapefile Conversion API"
echo "=================================="

# Configuration
API_URL="http://localhost:3000"
TEST_FILE="C:/Users/<USER>/Downloads/Heron_test.zip"

# Check if API is running
echo "📡 Checking API health..."
HEALTH_RESPONSE=$(curl -s "$API_URL/api/health")
if [ $? -eq 0 ]; then
    echo "✅ API is running: $HEALTH_RESPONSE"
else
    echo "❌ API is not running. Please start with: npm start"
    exit 1
fi

# Check if test file exists
if [ ! -f "$TEST_FILE" ]; then
    echo "❌ Test file not found: $TEST_FILE"
    echo "Please update the TEST_FILE variable in this script"
    exit 1
fi

echo "✅ Test file found: $(basename "$TEST_FILE")"

# Test 1: Analyze the shapefile
echo ""
echo "📊 Analyzing shapefile..."
ANALYZE_RESPONSE=$(curl -s -X POST \
    -F "shapefile=@$TEST_FILE" \
    "$API_URL/api/analyze")

if [ $? -eq 0 ]; then
    echo "✅ Analysis response:"
    echo "$ANALYZE_RESPONSE" | python -m json.tool 2>/dev/null || echo "$ANALYZE_RESPONSE"
else
    echo "❌ Analysis failed"
    exit 1
fi

# Test 2: Convert the shapefile
echo ""
echo "🔄 Converting shapefile..."
OUTPUT_FILE="output/$(basename "$TEST_FILE" .zip)_converted.zip"

# Create output directory if it doesn't exist
mkdir -p output

CONVERT_RESPONSE=$(curl -s -X POST \
    -F "shapefile=@$TEST_FILE" \
    "$API_URL/api/convert" \
    -o "$OUTPUT_FILE" \
    -w "%{http_code}")

if [ "$CONVERT_RESPONSE" = "200" ]; then
    echo "✅ Conversion successful!"
    echo "   Output saved to: $OUTPUT_FILE"
    echo "   File size: $(ls -lh "$OUTPUT_FILE" | awk '{print $5}')"
else
    echo "❌ Conversion failed (HTTP $CONVERT_RESPONSE)"
    # Try to show error message
    if [ -f "$OUTPUT_FILE" ]; then
        echo "Error details:"
        cat "$OUTPUT_FILE"
        rm "$OUTPUT_FILE"
    fi
fi

echo ""
echo "=================================="
echo "🎉 Test completed!"
echo ""
echo "💡 To test a different file:"
echo "   Edit the TEST_FILE variable in this script"
