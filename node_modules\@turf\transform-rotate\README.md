# @turf/transform-rotate

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

## transformRotate

Rotates any geojson Feature or Geometry of a specified angle, around its `centroid` or a given `pivot` point;
all rotations follow the right-hand rule: [https://en.wikipedia.org/wiki/Right-hand_rule][1]

**Parameters**

-   `geojson` **[GeoJSON][2]** object to be rotated
-   `angle` **[number][3]** of rotation (along the vertical axis), from North in decimal degrees, negative clockwise
-   `options` **[Object][4]** Optional parameters (optional, default `{}`)
    -   `options.pivot` **[Coord][5]** point around which the rotation will be performed (optional, default `'centroid'`)
    -   `options.mutate` **[boolean][6]** allows GeoJSON input to be mutated (significant performance increase if true) (optional, default `false`)

**Examples**

```javascript
var poly = turf.polygon([[[0,29],[3.5,29],[2.5,32],[0,29]]]);
var options = {pivot: [0, 25]};
var rotatedPoly = turf.transformRotate(poly, 10, options);

//addToMap
var addToMap = [poly, rotatedPoly];
rotatedPoly.properties = {stroke: '#F00', 'stroke-width': 4};
```

Returns **[GeoJSON][2]** the rotated GeoJSON feature

[1]: https://en.wikipedia.org/wiki/Right-hand_rule

[2]: https://tools.ietf.org/html/rfc7946#section-3

[3]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Number

[4]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Object

[5]: https://tools.ietf.org/html/rfc7946#section-3.1.1

[6]: https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Boolean

<!-- This file is automatically generated. Please don't edit it directly:
if you find an error, edit the source file (likely index.js), and re-run
./scripts/generate-readmes in the turf project. -->

---

This module is part of the [Turfjs project](http://turfjs.org/), an open source
module collection dedicated to geographic algorithms. It is maintained in the
[Turfjs/turf](https://github.com/Turfjs/turf) repository, where you can create
PRs and issues.

### Installation

Install this module individually:

```sh
$ npm install @turf/transform-rotate
```

Or install the Turf module that includes it as a function:

```sh
$ npm install @turf/turf
```
